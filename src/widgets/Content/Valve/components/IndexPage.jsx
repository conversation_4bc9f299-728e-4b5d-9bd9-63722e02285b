import React, { useEffect, useRef } from 'react';
import * as PIXI from 'pixi.js';

/**
 * 阀门首页组件
 * 使用PixiJS实现高性能圆环
 */
const IndexPage = () => {
    const pixiContainerRef = useRef(null);
    const pixiAppRef = useRef(null);
    const segmentGraphicsRef = useRef([]); // 存储所有圆环段的引用
    const percentageTextRef = useRef(null); // 百分数文本引用
    const triangleIndicatorRef = useRef(null); // 三角形指示器引用
    const animationRef = useRef(null); // 动画引用
    const currentProgressRef = useRef(0); // 当前进度

    useEffect(() => {
        if (!pixiContainerRef.current) return;

        const initializePixi = async () => {
            // 创建PixiJS应用
            const app = new PIXI.Application();
            await app.init({
                width: 200,
                height: 200,
                backgroundColor: 0x000000,
                backgroundAlpha: 0, // 透明背景
                antialias: true,
                resolution: window.devicePixelRatio * 2 || 2, // 提高分辨率
                autoDensity: true,
            });

            pixiAppRef.current = app;
            pixiContainerRef.current.appendChild(app.canvas);

            // 创建圆环容器
            const ringContainer = new PIXI.Container();
            ringContainer.x = app.screen.width / 2;
            ringContainer.y = app.screen.height / 2;
            app.stage.addChild(ringContainer);

            // 圆环参数
            const totalSegments = 20;
            const radius = 80; // 再次放大半径
            const segmentHeight = 14; // 增加段的径向厚度
            const gapAngle = 2; // 间隙角度
            const segmentAngle = (360 / totalSegments) - gapAngle; // 每段角度减去间隙

            // 绘制圆环段的函数 - 优化渲染细节
            const drawSegment = (graphics, startAngle, endAngle, color, alpha = 1.0) => {
                graphics.clear();

                // 使用更精细的路径绘制
                const outerRadius = radius;
                const innerRadius = radius - segmentHeight;

                // 计算路径点，使用更多的中间点来平滑曲线
                const steps = 8; // 增加步数以获得更平滑的弧线
                const angleStep = (endAngle - startAngle) / steps;

                // 开始路径
                graphics.moveTo(
                    Math.cos(startAngle) * innerRadius,
                    Math.sin(startAngle) * innerRadius
                );

                // 绘制到外圆起点
                graphics.lineTo(
                    Math.cos(startAngle) * outerRadius,
                    Math.sin(startAngle) * outerRadius
                );

                // 绘制外弧 - 使用多个小段来提高平滑度
                for (let i = 1; i <= steps; i++) {
                    const angle = startAngle + angleStep * i;
                    graphics.lineTo(
                        Math.cos(angle) * outerRadius,
                        Math.sin(angle) * outerRadius
                    );
                }

                // 绘制到内圆终点
                graphics.lineTo(
                    Math.cos(endAngle) * innerRadius,
                    Math.sin(endAngle) * innerRadius
                );

                // 绘制内弧 - 反向
                for (let i = steps - 1; i >= 0; i--) {
                    const angle = startAngle + angleStep * i;
                    graphics.lineTo(
                        Math.cos(angle) * innerRadius,
                        Math.sin(angle) * innerRadius
                    );
                }

                // 闭合路径并填充
                graphics.closePath();
                graphics.fill({ color: color, alpha: alpha });
            };

            // 圆环段渐变动画函数（简化版本）
            const animateSegmentColor = (graphics, startAngle, endAngle, fromColor, toColor, duration = 200) => {
                const steps = 8;
                const stepDuration = duration / steps;
                let currentStep = 0;

                const colorAnimation = setInterval(() => {
                    currentStep++;
                    const progress = currentStep / steps;

                    // 使用缓动函数
                    const easeProgress = 1 - Math.pow(1 - progress, 2); // ease-out quad

                    // 简单的颜色插值：直接在两种颜色之间插值
                    let interpolatedColor;
                    if (toColor === 0x20A461) {
                        // 从灰色(0x4D4D4F)到绿色(0x20A461)
                        const r1 = (fromColor >> 16) & 0xFF;
                        const g1 = (fromColor >> 8) & 0xFF;
                        const b1 = fromColor & 0xFF;

                        const r2 = (toColor >> 16) & 0xFF;
                        const g2 = (toColor >> 8) & 0xFF;
                        const b2 = toColor & 0xFF;

                        const r = Math.round(r1 + (r2 - r1) * easeProgress);
                        const g = Math.round(g1 + (g2 - g1) * easeProgress);
                        const b = Math.round(b1 + (b2 - b1) * easeProgress);

                        interpolatedColor = (r << 16) | (g << 8) | b;
                    } else {
                        // 从绿色到灰色
                        const r1 = (fromColor >> 16) & 0xFF;
                        const g1 = (fromColor >> 8) & 0xFF;
                        const b1 = fromColor & 0xFF;

                        const r2 = (toColor >> 16) & 0xFF;
                        const g2 = (toColor >> 8) & 0xFF;
                        const b2 = toColor & 0xFF;

                        const r = Math.round(r1 + (r2 - r1) * easeProgress);
                        const g = Math.round(g1 + (g2 - g1) * easeProgress);
                        const b = Math.round(b1 + (b2 - b1) * easeProgress);

                        interpolatedColor = (r << 16) | (g << 8) | b;
                    }

                    drawSegment(graphics, startAngle, endAngle, interpolatedColor, 1.0);

                    if (currentStep >= steps) {
                        clearInterval(colorAnimation);
                        // 确保最终状态正确
                        drawSegment(graphics, startAngle, endAngle, toColor, 1.0);
                    }
                }, stepDuration);
            };

            // 恢复到当前进度状态的函数 - 直接设置，避免闪烁
            const restoreToCurrentProgress = () => {
                // 只有在没有动画进行时才恢复
                if (!animationRef.current) {
                    segmentGraphicsRef.current.forEach((graphics, index) => {
                        const anglePerSegment = 360 / totalSegments;
                        const startAngle = (index * anglePerSegment - 90) * Math.PI / 180;
                        const endAngle = ((index * anglePerSegment + segmentAngle - 90) * Math.PI / 180);

                        // 根据当前进度决定颜色，直接设置避免闪烁
                        const color = index < currentProgressRef.current ? 0x20A461 : 0x4D4D4F;
                        drawSegment(graphics, startAngle, endAngle, color);
                    });

                    // 恢复百分数显示
                    const percentage = Math.round((currentProgressRef.current / totalSegments) * 100);
                    if (percentageTextRef.current) {
                        percentageTextRef.current.text = `${percentage}%`;
                    }
                }
            };

            // 立即更新所有段到当前进度状态（用于动画完成后的状态同步）
            const updateAllSegmentsToCurrentProgress = () => {
                segmentGraphicsRef.current.forEach((graphics, index) => {
                    const anglePerSegment = 360 / totalSegments;
                    const startAngle = (index * anglePerSegment - 90) * Math.PI / 180;
                    const endAngle = ((index * anglePerSegment + segmentAngle - 90) * Math.PI / 180);

                    // 根据当前进度决定颜色（直接设置，不使用动画）
                    const color = index < currentProgressRef.current ? 0x20A461 : 0x4D4D4F;
                    drawSegment(graphics, startAngle, endAngle, color);
                });
            };

            // 手动重置函数（可以在需要时调用）
            const forceReset = () => {
                // 停止当前动画
                if (animationRef.current) {
                    clearInterval(animationRef.current);
                    animationRef.current = null;
                }

                segmentGraphicsRef.current.forEach((graphics, index) => {
                    const anglePerSegment = 360 / totalSegments;
                    const startAngle = (index * anglePerSegment - 90) * Math.PI / 180;
                    const endAngle = ((index * anglePerSegment + segmentAngle - 90) * Math.PI / 180);
                    drawSegment(graphics, startAngle, endAngle, 0x4D4D4F);
                });

                // 重置进度和三角形位置
                currentProgressRef.current = 0;
                updateTrianglePosition(0);

                // 更新百分数为0%
                if (percentageTextRef.current) {
                    percentageTextRef.current.text = '0%';
                }
            };

            // 平滑移动三角形的函数（真正的平滑移动）
            const animateTriangleSmooth = (targetProgress) => {
                if (!triangleIndicatorRef.current) return;

                const anglePerSegment = 360 / totalSegments;
                const startAngle = (currentProgressRef.current * anglePerSegment - 90) * Math.PI / 180;
                const targetAngle = (targetProgress * anglePerSegment - 90) * Math.PI / 180;
                const triangleDistance = radius + 8;

                // 计算角度差，选择最短路径
                let angleDiff = targetAngle - startAngle;
                if (angleDiff > Math.PI) angleDiff -= 2 * Math.PI;
                if (angleDiff < -Math.PI) angleDiff += 2 * Math.PI;

                const totalSteps = Math.abs(targetProgress - currentProgressRef.current);
                if (totalSteps <= 0) return;

                // 计算总动画时间和平滑步数
                const totalAnimationTime = totalSteps * 300; // 总时间
                const smoothSteps = Math.max(30, totalSteps * 10); // 至少30步，确保平滑
                const stepDuration = totalAnimationTime / smoothSteps;


                let currentStep = 0;

                const triangleAnimation = setInterval(() => {
                    currentStep++;
                    const progress = currentStep / smoothSteps;

                    // 使用缓动函数让移动更自然
                    const easeProgress = 1 - Math.pow(1 - progress, 3); // ease-out cubic
                    const interpolatedAngle = startAngle + angleDiff * easeProgress;

                    triangleIndicatorRef.current.x = Math.cos(interpolatedAngle) * triangleDistance;
                    triangleIndicatorRef.current.y = Math.sin(interpolatedAngle) * triangleDistance;
                    triangleIndicatorRef.current.rotation = interpolatedAngle + Math.PI / 2;

                    if (currentStep >= smoothSteps) {
                        clearInterval(triangleAnimation);
                    }
                }, stepDuration);
            };

            // 立即更新三角形位置的函数（不带动画）
            const updateTrianglePosition = (progress) => {
                if (triangleIndicatorRef.current) {
                    const anglePerSegment = 360 / totalSegments;
                    const currentAngle = (progress * anglePerSegment - 90) * Math.PI / 180;
                    const triangleDistance = radius + 8; // 与动画函数保持一致

                    triangleIndicatorRef.current.x = Math.cos(currentAngle) * triangleDistance;
                    triangleIndicatorRef.current.y = Math.sin(currentAngle) * triangleDistance;
                    triangleIndicatorRef.current.rotation = currentAngle + Math.PI / 2; // 修正旋转让尖端指向圆环
                }
            };

            // 高亮从0到指定索引的所有段（悬停效果）- 使用渐变动画
            const highlightSegmentsTo = (targetIndex) => {
                segmentGraphicsRef.current.forEach((graphics, index) => {
                    const anglePerSegment = 360 / totalSegments;
                    const startAngle = (index * anglePerSegment - 90) * Math.PI / 180;
                    const endAngle = ((index * anglePerSegment + segmentAngle - 90) * Math.PI / 180);

                    let targetColor;
                    let currentColor;

                    if (index <= targetIndex) {
                        // 如果是已执行的段（绿色），悬停时显示高亮绿色
                        if (index < currentProgressRef.current) {
                            currentColor = 0x20A461; // 当前绿色
                            targetColor = 0x23C76D; // 高亮绿色
                        } else {
                            currentColor = 0x4D4D4F; // 当前灰色
                            targetColor = 0xB6B4B8; // 预览灰色
                        }
                    } else {
                        currentColor = 0x4D4D4F; // 当前灰色
                        targetColor = 0x4D4D4F; // 保持灰色
                    }

                    // 只有颜色不同时才执行动画
                    if (currentColor !== targetColor) {
                        animateSegmentColor(graphics, startAngle, endAngle, currentColor, targetColor, 150);
                    } else {
                        drawSegment(graphics, startAngle, endAngle, targetColor);
                    }
                });

                // 更新百分数
                const percentage = Math.round(((targetIndex + 1) / totalSegments) * 100);
                if (percentageTextRef.current) {
                    percentageTextRef.current.text = `${percentage}%`;
                }
            };

            // 智能动画到指定索引的函数
            const animateToIndex = (targetIndex) => {
                // 停止当前动画
                if (animationRef.current) {
                    clearInterval(animationRef.current);
                }

                const targetProgress = targetIndex + 1;
                const currentProgress = currentProgressRef.current;

                // 如果目标位置等于当前位置，不执行动画
                if (targetProgress === currentProgress) {
                    return;
                }

                const isForward = targetProgress > currentProgress;
                const animationSpeed = 300; // 每段动画间隔300ms

                // 启动三角形的平滑移动动画
                animateTriangleSmooth(targetProgress);

                if (isForward) {
                    // 前进动画：从当前位置到目标位置
                    let currentIndex = currentProgress; // 从当前进度开始

                    animationRef.current = setInterval(() => {
                        // 使用渐变动画更新当前段为绿色
                        const anglePerSegment = 360 / totalSegments;
                        const startAngle = (currentIndex * anglePerSegment - 90) * Math.PI / 180;
                        const endAngle = ((currentIndex * anglePerSegment + segmentAngle - 90) * Math.PI / 180);

                        // 使用渐变动画
                        animateSegmentColor(
                            segmentGraphicsRef.current[currentIndex],
                            startAngle,
                            endAngle,
                            0x4D4D4F,
                            0x20A461,
                            200
                        );

                        currentIndex++;

                        // 更新百分数和进度
                        const percentage = Math.round((currentIndex / totalSegments) * 100);
                        if (percentageTextRef.current) {
                            percentageTextRef.current.text = `${percentage}%`;
                        }
                        currentProgressRef.current = currentIndex;

                        // 动画完成
                        if (currentIndex > targetIndex) {
                            clearInterval(animationRef.current);
                            animationRef.current = null;
                            // 不立即调用updateAllSegmentsToCurrentProgress，让渐变动画自然完成
                        }
                    }, animationSpeed);
                } else {
                    // 倒退动画：从当前位置逐个倒退到目标位置
                    let currentIndex = currentProgress - 1; // 从当前进度的前一个开始倒退

                    animationRef.current = setInterval(() => {
                        // 只处理当前这一个段，使用渐变动画变回灰色
                        const anglePerSegment = 360 / totalSegments;
                        const startAngle = (currentIndex * anglePerSegment - 90) * Math.PI / 180;
                        const endAngle = ((currentIndex * anglePerSegment + segmentAngle - 90) * Math.PI / 180);

                        // 使用渐变动画变回灰色（只处理当前段）
                        animateSegmentColor(
                            segmentGraphicsRef.current[currentIndex],
                            startAngle,
                            endAngle,
                            0x20A461,
                            0x4D4D4F,
                            200
                        );

                        // 更新进度和百分数（基于当前处理的段）
                        currentProgressRef.current = currentIndex;
                        const percentage = Math.round((currentIndex / totalSegments) * 100);
                        if (percentageTextRef.current) {
                            percentageTextRef.current.text = `${percentage}%`;
                        }

                        // 递减索引，准备处理下一个段
                        currentIndex--;

                        // 动画完成：当到达目标位置时停止
                        if (currentIndex < targetIndex) {
                            clearInterval(animationRef.current);
                            animationRef.current = null;
                            // 确保最终进度正确
                            currentProgressRef.current = targetProgress;
                        }
                    }, animationSpeed);
                }
            };

            // 创建20个圆环段
            segmentGraphicsRef.current = []; // 重置数组
            for (let i = 0; i < totalSegments; i++) {
                const graphics = new PIXI.Graphics();

                // 计算角度（从顶部开始，顺时针）
                const anglePerSegment = 360 / totalSegments;
                const startAngle = (i * anglePerSegment - 90) * Math.PI / 180;
                const endAngle = ((i * anglePerSegment + segmentAngle - 90) * Math.PI / 180);

                // 初始绘制
                drawSegment(graphics, startAngle, endAngle, 0x4D4D4F);

                // 添加交互性
                graphics.eventMode = 'static';
                graphics.cursor = 'pointer';

                // 悬停效果 - 高亮从0到当前段
                graphics.on('pointerover', () => {
                    // 只有在没有动画进行时才显示悬停效果
                    if (!animationRef.current) {
                        highlightSegmentsTo(i);
                    }
                });

                // 鼠标离开时恢复到当前进度状态
                graphics.on('pointerout', () => {
                    if (!animationRef.current) {
                        restoreToCurrentProgress();
                    }
                });

                // 点击事件 - 开始动画填充
                graphics.on('pointerdown', () => {
                    animateToIndex(i);
                });

                // 存储引用
                segmentGraphicsRef.current.push(graphics);
                ringContainer.addChild(graphics);
            }

            // 添加中间的百分数文本
            const percentageText = new PIXI.Text({
                text: '0%',
                style: {
                    fontFamily: 'ChakraPetch-Medium, monospace',
                    fontSize: 15, // 稍微调小字体
                    fill: 0x20A461,
                    align: 'center'
                }
            });

            // 居中显示
            percentageText.anchor.set(0.5);
            percentageText.x = 0;
            percentageText.y = 0;

            percentageTextRef.current = percentageText;
            ringContainer.addChild(percentageText);

            // 添加绿色三角形指示器
            const triangleIndicator = new PIXI.Graphics();
            const triangleWidth = 10; // 三角形底边宽度
            const triangleHeight = 6; // 三角形高度（更扁）
            const triangleDistance = radius + 8; // 三角形距离圆环中心的距离（拉近）

            // 绘制三角形（尖端指向圆环，底边朝外）
            triangleIndicator.fill({ color: 0x20A461, alpha: 1.0 }); // 绿色
            triangleIndicator.moveTo(0, 0); // 尖端（指向圆环中心）
            triangleIndicator.lineTo(-triangleWidth/2, -triangleHeight); // 左上角
            triangleIndicator.lineTo(triangleWidth/2, -triangleHeight); // 右上角
            triangleIndicator.closePath();
            triangleIndicator.fill();

            // 初始位置在12点位置
            triangleIndicator.x = 0;
            triangleIndicator.y = -triangleDistance;
            triangleIndicator.rotation = 0; // 修正初始旋转角度

            triangleIndicatorRef.current = triangleIndicator;
            ringContainer.addChild(triangleIndicator);

            // 添加刻度标记（25%、50%、75%）
            // 计算实际的角度位置：25% = 5段，50% = 10段，75% = 15段
            const scalePositions = [
                { percentage: 25, segmentIndex: 5, position: 'right' },   // 25% = 第5段
                { percentage: 50, segmentIndex: 10, position: 'bottom' }, // 50% = 第10段
                { percentage: 75, segmentIndex: 15, position: 'left' }    // 75% = 第15段
            ];

            scalePositions.forEach(({ percentage, segmentIndex, position }) => {
                // 刻度线
                const scaleLine = new PIXI.Graphics();
                // 计算正确的角度：基于段索引和圆环的角度系统
                const anglePerSegment = 360 / totalSegments;
                const scaleAngle = (segmentIndex * anglePerSegment - 90) * Math.PI / 180; // -90度让0度在顶部
                const innerRadius = radius + 2;
                const outerRadius = radius + 12;

                scaleLine.stroke({ color: 0x909999, width: 2 });
                scaleLine.moveTo(
                    Math.cos(scaleAngle) * innerRadius,
                    Math.sin(scaleAngle) * innerRadius
                );
                scaleLine.lineTo(
                    Math.cos(scaleAngle) * outerRadius,
                    Math.sin(scaleAngle) * outerRadius
                );
                scaleLine.stroke();

                ringContainer.addChild(scaleLine);

                // 刻度文字
                const scaleText = new PIXI.Text({
                    text: `${percentage}%`,
                    style: {
                        fontFamily: 'ChakraPetch-Medium, monospace',
                        fontSize: 10,
                        fill: 0x909999,
                        align: 'center'
                    }
                });

                // 根据位置调整文字位置
                const textDistance = radius + 20;
                const textX = Math.cos(scaleAngle) * textDistance;
                const textY = Math.sin(scaleAngle) * textDistance;

                // 调整锚点以便更好地定位
                switch (position) {
                    case 'right':
                        scaleText.anchor.set(0, 0.5); // 左对齐，垂直居中
                        break;
                    case 'bottom':
                        scaleText.anchor.set(0.5, 0); // 水平居中，顶对齐
                        break;
                    case 'left':
                        scaleText.anchor.set(1, 0.5); // 右对齐，垂直居中
                        break;
                }

                scaleText.x = textX;
                scaleText.y = textY;

                ringContainer.addChild(scaleText);
            });
        };

        initializePixi();

        // 清理函数
        return () => {
            if (pixiAppRef.current) {
                pixiAppRef.current.destroy(true, true);
                pixiAppRef.current = null;
            }
        };
    }, []);

    return (
        <div className="flow-page w-full h-full flex flex-col items-center justify-center">
            {/* PixiJS圆环容器 */}
            <div
                ref={pixiContainerRef}
                className="pixi-ring-container flex items-center justify-center"
                style={{ width: '200px', height: '200px' }}
            />
        </div>
    );
};

export default IndexPage;