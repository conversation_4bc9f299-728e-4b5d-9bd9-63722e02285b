import { useRef, useEffect, useState } from 'react';
import * as PIXI from 'pixi.js';

// 🎨 PixiJS 高性能工况图组件
const Lmh2 = () => {
    const canvasRef = useRef(null);
    const appRef = useRef(null);
    const mainContainerRef = useRef(null); // 保存主容器引用
    const waterFlowLinesRef = useRef([]); // 保存水流线条引用
    const animationRef = useRef(null); // 保存动画引用
    const fanRotationRef = useRef([]); // 保存风扇旋转引用
    const [dimensions, setDimensions] = useState({ width: 0, height: 0 });

    // 监听容器尺寸变化
    useEffect(() => {
        const updateDimensions = () => {
            if (canvasRef.current) {
                const { clientWidth, clientHeight } = canvasRef.current;
                setDimensions({ width: clientWidth, height: clientHeight });
            }
        };

        // 初始化尺寸
        updateDimensions();

        // 监听窗口大小变化
        window.addEventListener('resize', updateDimensions);

        // 使用 ResizeObserver 监听容器大小变化
        const resizeObserver = new ResizeObserver(updateDimensions);
        if (canvasRef.current) {
            resizeObserver.observe(canvasRef.current);
        }

        return () => {
            window.removeEventListener('resize', updateDimensions);
            resizeObserver.disconnect();
        };
    }, []);

    // 🎯 初始化 PIXI 应用（只执行一次）
    useEffect(() => {
        if (dimensions.width === 0 || dimensions.height === 0 || appRef.current) return;

        let isMounted = true;

        const app = new PIXI.Application();

        const initApp = async () => {
            try {
                await app.init({
                    width: dimensions.width,
                    height: dimensions.height,
                    backgroundColor: 0x000000,
                    backgroundAlpha: 0,
                    antialias: true,
                    resolution: window.devicePixelRatio || 1,
                    autoDensity: true,
                });

                if (!isMounted || !canvasRef.current) {
                    app.destroy(true);
                    return;
                }

                appRef.current = app;

                if (canvasRef.current) {
                    canvasRef.current.innerHTML = '';
                }

                if (canvasRef.current && app.canvas) {
                    canvasRef.current.appendChild(app.canvas);
                    app.canvas.style.width = '100%';
                    app.canvas.style.height = '100%';
                    app.canvas.style.objectFit = 'contain';
                }

                if (!isMounted) {
                    app.destroy(true);
                    return;
                }

                const waterTexture = startWaterFlowAnimation(app);
                drawFlowChart(app, dimensions, waterTexture);

            } catch (error) {
                console.error('PIXI 应用初始化失败:', error);
                if (app) {
                    try {
                        app.destroy(true);
                    } catch (destroyError) {
                        console.error('PIXI 应用销毁失败:', destroyError);
                    }
                }
            }
        };

        initApp();

        return () => {
            isMounted = false;
        };
    }, [dimensions.width, dimensions.height]);

    // 🧹 应用清理（仅在组件卸载时执行）
    useEffect(() => {
        return () => {
            if (animationRef.current && appRef.current) {
                try {
                    appRef.current.ticker.remove(animationRef.current);
                } catch (error) {
                    console.error('移除动画失败:', error);
                }
                animationRef.current = null;
            }

            if (appRef.current) {
                try {
                    if (canvasRef.current && appRef.current.canvas && canvasRef.current.contains(appRef.current.canvas)) {
                        canvasRef.current.removeChild(appRef.current.canvas);
                    }
                    appRef.current.destroy(true, {
                        children: true,
                        texture: true,
                        baseTexture: true
                    });
                } catch (error) {
                    console.error('PIXI 应用清理失败:', error);
                } finally {
                    appRef.current = null;
                    mainContainerRef.current = null;
                    waterFlowLinesRef.current = [];
                    fanRotationRef.current = [];
                }
            }
        };
    }, []);

    // 🔄 处理尺寸变化（只更新缩放，不重新绘制）
    useEffect(() => {
        if (!appRef.current || !mainContainerRef.current) return;
        if (dimensions.width === 0 || dimensions.height === 0) return;

        try {
            appRef.current.renderer.resize(dimensions.width, dimensions.height);

            if (appRef.current.canvas) {
                appRef.current.canvas.style.width = '100%';
                appRef.current.canvas.style.height = '100%';
            }

            updateContainerScale(mainContainerRef.current, dimensions);
        } catch (error) {
            console.error('尺寸更新失败:', error);
        }
    }, [dimensions.width, dimensions.height]);

    // 🔄 更新容器缩放（用于尺寸变化时）
    const updateContainerScale = (mainContainer, dimensions) => {
        if (!mainContainer || dimensions.width <= 0 || dimensions.height <= 0) return;

        try {
            // 计算工况图的实际边界
            // 左侧：流量计在x=10，宽度160（-80到+80），所以左边界是10-80=-70
            // 右侧：压力计在x=1380，宽度160（-80到+80），所以右边界是1380+80=1460
            // 上下边界需要考虑新的管道系统：上方到y=60，下方到y=340
            const actualLeft = -70;   // 左侧流量计的左边界
            const actualRight = 1460; // 右侧压力计的右边界
            const actualTop = 60;     // 上方管道的上边界
            const actualBottom = 340; // 下方管道的下边界

            const actualWidth = actualRight - actualLeft;   // 1410
            const actualHeight = actualBottom - actualTop;  // 230

            // 使用较小的边距，让工况图更好地填充容器
            const padding = 10; // 减小边距到10px
            const availableWidth = Math.max(dimensions.width - padding * 2, 100);
            const availableHeight = Math.max(dimensions.height - padding * 2, 100);

            // 计算缩放比例，让工况图尽可能填充容器
            const scaleX = availableWidth / actualWidth;
            const scaleY = availableHeight / actualHeight;

            // 使用较大的缩放比例，让工况图更好地填充容器
            const scale = Math.min(scaleX, scaleY);

            // 确保缩放值在合理范围内
            const finalScale = Math.max(Math.min(scale, 3), 0.1); // 扩大缩放范围：0.1-3

            // 更新缩放
            mainContainer.scale.set(finalScale);

            // 计算实际显示尺寸
            const scaledWidth = actualWidth * finalScale;
            const scaledHeight = actualHeight * finalScale;

            // 重新居中显示 - 考虑实际边界偏移
            const offsetX = actualLeft * finalScale; // 左边界偏移
            const offsetY = actualTop * finalScale;  // 上边界偏移

            mainContainer.x = (dimensions.width - scaledWidth) / 2 - offsetX;
            mainContainer.y = (dimensions.height - scaledHeight) / 2 - offsetY;
        } catch (error) {
            console.error('更新容器缩放失败:', error);
        }
    };

    // 🎨 绘制完整的工况图（只执行一次）
    const drawFlowChart = (app, dimensions, waterTexture) => {
        // 创建主容器
        const mainContainer = new PIXI.Container();

        // 保存主容器引用，用于后续缩放更新
        mainContainerRef.current = mainContainer;

        // 应用初始缩放
        updateContainerScale(mainContainer, dimensions);

        app.stage.addChild(mainContainer);

        const graphics = new PIXI.Graphics();
        mainContainer.addChild(graphics);

        // 🔗 绘制主管道系统
        drawPipelineSystem(graphics, mainContainer, waterTexture);

        // 📊 绘制仪表组件
        drawInstruments(mainContainer, waterTexture);

        // ⚙️ 绘制阀门组件
        drawValves(mainContainer);
    };

    // 🌊 创建水流纹理
    const createWaterFlowTexture = () => {
        const canvas = document.createElement('canvas');
        canvas.width = 24;
        canvas.height = 6;
        const ctx = canvas.getContext('2d');

        // 清除画布
        ctx.clearRect(0, 0, canvas.width, canvas.height);

        // 绘制虚线水流图案
        ctx.fillStyle = '#00ff88';
        // 第一段虚线
        ctx.fillRect(0, 1, 10, 4);
        // 间隔
        // 第二段虚线
        ctx.fillRect(16, 1, 8, 4);

        return PIXI.Texture.from(canvas);
    };

    // 🌊 启动水流动画
    const startWaterFlowAnimation = (app) => {
        const waterTexture = createWaterFlowTexture(app);

        const animateWaterFlow = () => {
            const flowSpeed = 0.4; // 统一流动速度
            waterFlowLinesRef.current.forEach((line) => {
                if (line && line.tilePosition) {
                    // 统一使用 customFlowDirection 控制流向
                    if (line.customFlowDirection === 'backward') {
                        line.tilePosition.x -= flowSpeed;
                    } else {
                        line.tilePosition.x += flowSpeed; // 默认为 'forward'
                    }
                }
            });

            // 更新风扇旋转动画
            fanRotationRef.current.forEach((fan) => {
                if (fan && fan.rotation !== undefined) {
                    fan.rotation += 0.04;
                }
            });
        };

        // 保存动画函数引用
        animationRef.current = animateWaterFlow;

        // 添加到ticker
        app.ticker.add(animateWaterFlow);

        return waterTexture;
    };

    // 🌊 创建水流线条
    const createWaterFlowLine = (container, x1, y1, x2, y2, waterTexture) => {
        if (!waterTexture) return; // 增加纹理保护
        const length = Math.sqrt((x2 - x1) * (x2 - x1) + (y2 - y1) * (y2 - y1));
        const angle = Math.atan2(y2 - y1, x2 - x1);

        const tilingSprite = new PIXI.TilingSprite({
            texture: waterTexture,
            width: length,
            height: 3
        });
        tilingSprite.x = x1;
        tilingSprite.y = y1;
        tilingSprite.rotation = angle;
        tilingSprite.anchor.set(0, 0.5);
        tilingSprite.customFlowDirection = 'forward'; // 明确流向

        container.addChild(tilingSprite);
        waterFlowLinesRef.current.push(tilingSprite);

        return tilingSprite;
    };

    // 🌊 创建指定颜色的水流线条
    const createColoredWaterFlowLine = (container, x1, y1, x2, y2, color, flowDirection = 'forward') => {
        try {
            // 创建指定颜色的水流纹理
            const canvas = document.createElement('canvas');
            canvas.width = 24;
            canvas.height = 6;
            const ctx = canvas.getContext('2d');

            if (!ctx) {
                console.error('无法获取canvas上下文');
                return null;
            }

            // 清除画布
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // 将颜色转换为CSS颜色字符串
            const colorStr = `#${color.toString(16).padStart(6, '0')}`;

            // 绘制虚线水流图案
            ctx.fillStyle = colorStr;
            // 第一段虚线
            ctx.fillRect(0, 1, 10, 4);
            // 间隔
            // 第二段虚线
            ctx.fillRect(16, 1, 8, 4);

            const colorTexture = PIXI.Texture.from(canvas);

            const length = Math.sqrt((x2 - x1) * (x2 - x1) + (y2 - y1) * (y2 - y1));
            const angle = Math.atan2(y2 - y1, x2 - x1);

            const tilingSprite = new PIXI.TilingSprite({
                texture: colorTexture,
                width: length,
                height: 3
            });
            tilingSprite.x = x1;
            tilingSprite.y = y1;
            tilingSprite.rotation = angle;
            tilingSprite.anchor.set(0, 0.5);

            // 添加流向标识，用于动画控制
            tilingSprite.customFlowDirection = flowDirection;
            tilingSprite.isColoredLine = true;

            container.addChild(tilingSprite);
            waterFlowLinesRef.current.push(tilingSprite);

            return tilingSprite;
        } catch (error) {
            console.error('创建彩色水流线条失败:', error);
            return null;
        }
    };

    // 🔗 绘制管道系统
    const drawPipelineSystem = (graphics, container, waterTexture) => {
        // 使用新的线条样式API
        graphics.setStrokeStyle({
            width: 3,
            color: 0x00ff88,
            alpha: 1
        });

        // 移除主水平管道，让泵机与进出口容器保持在同一水平线

        // 左侧进口管道连接（横向布局）- 水流管道
        // createWaterFlowLine(container, 90, 200, 140, 200, waterTexture);

        // 右侧出口管道连接（横向布局）- 水流管道
        // createWaterFlowLine(container, 1230, 200, 1300, 200, waterTexture);

        // 绘制泵机连接管道系统（上下连接线+横向连接线）
        const pumpColors = [
            0xff6b6b, // 红色 - 一号泵
            0x4ecdc4, // 青色 - 二号泵
            0x45b7d1, // 蓝色 - 三号泵
            0x96ceb4, // 绿色 - 四号泵
            0xfeca57, // 黄色 - 五号泵
            0xfd79a8  // 粉色 - 六号泵
        ];

        // 绘制每个泵机的上下连接线
        for (let i = 0; i < 6; i++) {
            const x = 435 + i * 100;

            // 一号泵使用水流样式（绿色），其他泵保持原色
            if (i === 0) {
                // 一号泵上下连接线使用水流样式 (从上往下)
                createWaterFlowLine(container, x, 60, x, 200, waterTexture);
                createWaterFlowLine(container, x, 200, x, 340, waterTexture);
            } else if (i === 2) {
                // 三号泵上方连接线改为绿色虚线流动效果 (从上往下)
                createWaterFlowLine(container, x, 60, x, 200, waterTexture);

                // 三号泵下方连接线也改为绿色虚线流动效果
                createWaterFlowLine(container, x, 200, x, 340, waterTexture);
            } else {
                // 其他泵改为灰色实线
                graphics.setStrokeStyle({ width: 2, color: 0x444444 }); // 调整为灰色

                // 上方连接线
                graphics.moveTo(x, 200);
                graphics.lineTo(x, 60);
                graphics.stroke();

                // 下方连接线
                graphics.moveTo(x, 200);
                graphics.lineTo(x, 340);
                graphics.stroke();
            }
        }

        // 绘制上方横向连接线（前两段为虚线，其他为灰色实线）

        // 绘制5段上方横线（连接6个泵机的垂直线）
        for (let i = 0; i < 5; i++) {
            const startX = 435 + i * 100;       // 当前泵机位置
            const endX = 435 + (i + 1) * 100;   // 下一个泵机位置
            const y = 60;                       // 上方横线高度

            if (i === 0 || i === 1) {
                // 第一段（红色）和第二段（蓝色）改为绿色虚线流动效果
                createWaterFlowLine(container, startX, y, endX, y, waterTexture);
            } else {
                // 其他段改为灰色实线
                graphics.setStrokeStyle({ width: 2, color: 0x444444 }); // 调整为灰色
                graphics.moveTo(startX, y);
                graphics.lineTo(endX, y);
                graphics.stroke();
            }
        }

        // 绘制5段下方横线（连接6个泵机的垂直线）- 全部改为水流样式
        for (let i = 0; i < 5; i++) {
            const startX = 435 + i * 100;       // 当前泵机位置
            const endX = 435 + (i + 1) * 100;   // 下一个泵机位置
            const y = 340;                      // 下方横线高度

            createWaterFlowLine(container, startX, y, endX, y, waterTexture);
        }

        // 绘制左侧直线连接（进口压力计右侧 → 顶部红线左侧）- 使用橙色便于观察
        const startX1 = 310;  // 进口压力计右侧 (230+80)
        const startY1 = 200;  // 压力计高度
        const endX1 = 435;    // 顶部红线左侧（一号泵位置，红线起点）
        const endY1 = 60;     // 顶部红线高度

        // [反向绘制] 为了控制水流方向，我们反转了线条的起点和终点，并使用 'backward' 流动 -> 改为标准绿色水流
        createWaterFlowLine(container, startX1, startY1, endX1, endY1, waterTexture);

        // 绘制右侧直线连接（底部横线右侧端 → 出口流量计左侧）- 使用紫色便于观察
        const startX2 = 935;  // 底部横线右侧端（六号泵位置，935 = 435 + 5*100）
        const startY2 = 340;  // 底部横线高度
        const endX2 = 1160 - 80;   // 出口流量计左侧 (1160-80)
        const endY2 = 200;    // 流量计高度

        // [反向绘制] 为了控制水流方向，我们反转了线条的起点和终点，并使用 'backward' 流动 -> 改为标准绿色水流
        createWaterFlowLine(container, startX2, startY2, endX2, endY2, waterTexture);

        // 绘制连接节点
        graphics.setStrokeStyle({ width: 0 });
        graphics.setFillStyle({ color: 0x00ff88 });

        // 移除所有连接点，保持简洁的设计
        const connectionPoints = [];

        connectionPoints.forEach(([x, y]) => {
            graphics.circle(x, y, 4);
            graphics.fill();
        });
    };

    // 📊 绘制仪表组件
    const drawInstruments = (mainContainer, waterTexture) => {
        // 🌊 左侧进口流量计（向左移动增加间距）
        createFlowMeter(mainContainer, 10, 200, '进口流量计');

        // 左侧连接线（流量计 -> 压力计），精确对齐两侧容器边缘
        createWaterFlowLine(mainContainer, 10 + 80, 200, 230 - 80, 200, waterTexture);

        // 🔽 左侧进口压力计（横向布局，与流量计同一水平线，保持距离）
        createPressureMeter(mainContainer, 230, 200, '进口压力计');

        // 🌊 右侧出口流量计（横向布局，与进口保持一致，去掉数据）
        createFlowMeter(mainContainer, 1160, 200, '出口流量计');

        // 右侧连接线（压力计 -> 流量计），修正长度和方向
        createWaterFlowLine(mainContainer, 1160 + 80, 200, 1380 - 80, 200, waterTexture);

        // 🔽 右侧出口压力计（向右移动增加间距）
        createPressureMeter(mainContainer, 1380, 200, '出口压力计');
    };

    // ⚙️ 绘制阀门组件
    const drawValves = (mainContainer) => {
        const valveNames = ['一号泵', '二号泵', '三号泵', '四号泵', '五号泵', '六号泵'];

        valveNames.forEach((name, index) => {
            // 计算居中位置：主管道从305到1065，中心点是685，6个泵机总宽度500，起始位置435
            const x = 435 + index * 100; // 调整起始位置为435以实现居中
            const y = 200; // 与进出口容器保持在同一水平线

            // 一号泵（index=0）和三号泵（index=2）启用旋转效果
            const shouldRotate = index === 0 || index === 2;
            createValve(mainContainer, x, y, name, shouldRotate);
        });
    };

    // 🌊 创建流量计组件
    const createFlowMeter = (parentContainer, x, y, title, mainValue = '', unit = '', totalValue = '') => {
        const container = new PIXI.Container();
        container.x = x;
        container.y = y;

        // 背景框（稍微缩小宽度，透明背景）
        const bg = new PIXI.Graphics();
        bg.setStrokeStyle({ width: 1, color: 0x00ff88 });
        bg.roundRect(-80, -35, 160, 70, 8);
        bg.stroke();
        container.addChild(bg);

        // 流量计图标（调整位置适应新容器）
        const icon = new PIXI.Graphics();
        icon.setFillStyle({ color: 0x00ff88 });
        icon.circle(-50, 0, 15);
        icon.fill();

        // 添加刻度线
        for (let i = 0; i < 8; i++) {
            const angle = (i / 8) * Math.PI * 2;
            const startX = -50 + Math.cos(angle) * 10;
            const startY = Math.sin(angle) * 10;
            const endX = -50 + Math.cos(angle) * 12;
            const endY = Math.sin(angle) * 12;

            icon.setStrokeStyle({ width: 1, color: 0x1a1a1a });
            icon.moveTo(startX, startY);
            icon.lineTo(endX, endY);
            icon.stroke();
        }
        container.addChild(icon);

        // 主数值（只在有数据时显示）
        if (mainValue && unit) {
            const mainText = new PIXI.Text({
                text: `${mainValue} ${unit}`,
                style: {
                    fontFamily: 'Arial',
                    fontSize: 14,
                    fill: 0x00ff88,
                    fontWeight: 'bold',
                }
            });
            mainText.x = -20;
            mainText.y = -10;
            container.addChild(mainText);
        }

        // 累计值（只在有数据时显示）
        if (totalValue) {
            const totalText = new PIXI.Text({
                text: totalValue,
                style: {
                    fontFamily: 'Arial',
                    fontSize: 10,
                    fill: 0xcccccc,
                }
            });
            totalText.x = -20;
            totalText.y = 8;
            container.addChild(totalText);
        }

        // 标题
        const titleText = new PIXI.Text({
            text: title,
            style: {
                fontFamily: 'Arial',
                fontSize: 12, // 稍微增大字体
                fill: 0xffffff, // 修改为白色
            }
        });
        titleText.x = -titleText.width / 2;
        titleText.y = -55;
        container.addChild(titleText);

        // 修复：添加到父容器而不是 app.stage
        parentContainer.addChild(container);
    };

    // 🔽 创建压力计组件
    const createPressureMeter = (parentContainer, x, y, title, value = '', unit = '') => {
        const container = new PIXI.Container();
        container.x = x;
        container.y = y;

        // 背景框（与流量计保持一致的尺寸：160x70，透明背景）
        const bg = new PIXI.Graphics();
        bg.setStrokeStyle({ width: 1, color: 0x4a9eff });
        bg.roundRect(-80, -35, 160, 70, 8);
        bg.stroke();
        container.addChild(bg);

        // 压力计图标（调整位置以适应新的容器尺寸）
        const icon = new PIXI.Graphics();
        icon.setFillStyle({ color: 0x4a9eff });
        icon.circle(-50, 0, 15);
        icon.fill();

        // 指针
        icon.setStrokeStyle({ width: 2, color: 0x1a1a1a });
        icon.moveTo(-50, 0);
        icon.lineTo(-50 + 10, -6);
        icon.stroke();
        container.addChild(icon);

        // 数值文本（只在有数据时显示）
        if (value && unit) {
            const valueText = new PIXI.Text({
                text: `${value} ${unit}`,
                style: {
                    fontFamily: 'Arial',
                    fontSize: 14,
                    fill: 0x4a9eff,
                    fontWeight: 'bold',
                }
            });
            valueText.x = -15;
            valueText.y = -10;
            container.addChild(valueText);
        }

        // 标题
        const titleText = new PIXI.Text({
            text: title,
            style: {
                fontFamily: 'Arial',
                fontSize: 12, // 稍微增大字体
                fill: 0xffffff, // 修改为白色
            }
        });
        titleText.x = -titleText.width / 2;
        titleText.y = -55;
        container.addChild(titleText);

        // 修复：添加到父容器而不是 app.stage
        parentContainer.addChild(container);
    };

    // ⚙️ 创建阀门组件
    const createValve = (parentContainer, x, y, name, shouldRotate = false) => {
        const container = new PIXI.Container();
        container.x = x;
        container.y = y;

        // 阀门背景
        const bg = new PIXI.Graphics();
        bg.setFillStyle({ color: 0x141414 }); // 去掉透明度
        bg.setStrokeStyle({ width: 1, color: 0xffffff }); // 边线修改细一点
        bg.roundRect(-30, -30, 60, 60, 8);
        bg.fill();
        bg.stroke();
        container.addChild(bg);

        // 泵机风扇（十字形状）
        const fan = new PIXI.Graphics();
        fan.setStrokeStyle({ width: 3, color: 0xffffff });

        // 绘制十字形状
        // 水平线
        fan.moveTo(-15, 0);
        fan.lineTo(15, 0);
        fan.stroke();

        // 垂直线
        fan.moveTo(0, -15);
        fan.lineTo(0, 15);
        fan.stroke();

        container.addChild(fan);

        // 如果需要旋转，将风扇添加到旋转引用中
        if (shouldRotate) {
            fanRotationRef.current.push(fan);
        }

        // 中心圆点
        const center = new PIXI.Graphics();
        center.setFillStyle({ color: 0xffffff });
        center.circle(0, 0, 2);
        center.fill();
        container.addChild(center);

        // 阀门名称背景遮罩
        const nameBg = new PIXI.Graphics();
        const nameText = new PIXI.Text({
            text: name,
            style: {
                fontFamily: 'Arial',
                fontSize: 10,
                fill: 0xffffff,
            }
        });

        // 计算文字尺寸并创建背景
        const textWidth = nameText.width;
        const textHeight = nameText.height;
        const padding = 2; // 背景边距

        nameBg.setFillStyle({ color: 0x141414 }); // 背景色改为#141414，无透明度
        nameBg.roundRect(-textWidth/2 - padding, 40 - padding, textWidth + padding*2, textHeight + padding*2, 3);
        nameBg.fill();
        container.addChild(nameBg);

        // 阀门名称
        nameText.x = -nameText.width / 2;
        nameText.y = 40;
        container.addChild(nameText);

        // 修复：添加到父容器而不是 app.stage
        parentContainer.addChild(container);
    };

    return (
        <div className="w-full h-full relative">
            <div
                ref={canvasRef}
                className="w-full h-full"
            />
        </div>
    );
};

export default Lmh2;
