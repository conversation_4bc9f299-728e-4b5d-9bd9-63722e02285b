import React, { useEffect } from 'react';
import NumberFlow from '@number-flow/react';
import { gsap } from 'gsap';

/**
 * 报警页面组件
 * 显示报警统计和报警记录列表
 */
const AlarmPage = ({ 
    alarmData,
    alarmListRef,
    alarmScrollContainerRef,
    scrollAnimationRef,
    alarmScrollPaused,
    setAlarmScrollPaused,
    setManualScrollPosition,
    animationTimelines,
    resourceRefs
}) => {
    
    // 启动自动滚动
    useEffect(() => {
        const startAutoScroll = () => {
            if (alarmScrollContainerRef.current && alarmListRef.current) {
                const containerHeight = alarmListRef.current.clientHeight;
                const contentHeight = alarmScrollContainerRef.current.scrollHeight;
                
                const scrollDistance = contentHeight > containerHeight * 2 ? 
                    -(contentHeight / 2) : 
                    -containerHeight;
                
                const scrollAnimation = gsap.to(alarmScrollContainerRef.current, {
                    y: scrollDistance,
                    duration: 16,
                    ease: 'none',
                    repeat: -1,
                    onRepeat: () => {
                        gsap.set(alarmScrollContainerRef.current, { y: 0 });
                    }
                });
                
                scrollAnimationRef.current = scrollAnimation;
                animationTimelines.current.push(scrollAnimation);
            }
        };
        
        const scrollTimer = setTimeout(startAutoScroll, 500);
        resourceRefs.current.timeouts.push(scrollTimer);
        
        return () => {
            clearTimeout(scrollTimer);
        };
    }, []);

    const handleMouseEnter = () => {
        if (scrollAnimationRef.current) {
            scrollAnimationRef.current.pause();
            setAlarmScrollPaused(true);
        }
    };

    const handleMouseLeave = () => {
        if (scrollAnimationRef.current && alarmScrollPaused && alarmScrollContainerRef.current && alarmListRef.current) {
            const currentY = gsap.getProperty(alarmScrollContainerRef.current, "y");
            const containerHeight = alarmListRef.current.clientHeight;
            const contentHeight = alarmScrollContainerRef.current.scrollHeight;
            
            let normalizedY = currentY;
            const maxScroll = -(contentHeight / 2);
            
            if (normalizedY > 0) {
                normalizedY = maxScroll + (normalizedY % Math.abs(maxScroll));
            } else if (normalizedY < maxScroll) {
                normalizedY = normalizedY % Math.abs(maxScroll);
            }
            
            scrollAnimationRef.current.kill();
            gsap.set(alarmScrollContainerRef.current, { y: normalizedY });
            
            const newTimeline = gsap.timeline({ repeat: -1 });
            
            newTimeline.to(alarmScrollContainerRef.current, {
                y: maxScroll,
                duration: Math.abs((maxScroll - normalizedY) / maxScroll) * 16,
                ease: 'none'
            })
            .set(alarmScrollContainerRef.current, { y: 0 })
            .to(alarmScrollContainerRef.current, {
                y: maxScroll,
                duration: 16,
                ease: 'none'
            });
            
            scrollAnimationRef.current = newTimeline;
            animationTimelines.current.push(newTimeline);
            
            setAlarmScrollPaused(false);
        }
    };

    const handleWheel = (e) => {
        if (alarmScrollPaused && alarmScrollContainerRef.current) {
            e.preventDefault();
            const currentY = gsap.getProperty(alarmScrollContainerRef.current, "y");
            const containerHeight = alarmListRef.current.clientHeight;
            const contentHeight = alarmScrollContainerRef.current.scrollHeight;
            
            const scrollStep = e.deltaY * 0.5;
            let newY = currentY - scrollStep;
            
            const maxScroll = -(contentHeight / 2);
            if (newY > 0) {
                newY = maxScroll;
            } else if (newY < maxScroll) {
                newY = 0;
            }
            
            gsap.set(alarmScrollContainerRef.current, { y: newY });
            setManualScrollPosition(newY);
        }
    };

    return (
        <div className="alarm-page w-full h-full flex flex-col gap-[5px] pt-[5px]">
            {/* 报警统计维度 */}
            <div className="grid grid-cols-2 gap-[5px] text-[12px] mb-[0px]">
                <div className="relative bg-gradient-to-br from-[#1a1a1d]/60 to-[#2A2A2E]/60 p-[6px] rounded-[4px] border border-[#E39D25]/20 transition-all duration-300 overflow-hidden">
                    <div className="text-[#909999] mb-[2px] font-['DingTalkJinBuTi'] text-[11px]">昨日报警</div>
                    <div className="text-[#E39D25] font-medium font-mono tracking-wider text-[14px]">
                        <NumberFlow
                            value={alarmData.yesterdayAlarms}
                            precision={0}
                            className="inline"
                            duration={800}
                        />
                        <span className="ml-[2px] text-[11px]">次</span>
                    </div>
                </div>
                
                <div className="relative bg-gradient-to-br from-[#1a1a1d]/60 to-[#2A2A2E]/60 p-[6px] rounded-[4px] border border-[#23C76D]/20 transition-all duration-300 overflow-hidden">
                    <div className="text-[#909999] mb-[2px] font-['DingTalkJinBuTi'] text-[11px]">今日报警</div>
                    <div className="text-[#23C76D] font-medium font-mono tracking-wider text-[14px]">
                        <NumberFlow
                            value={alarmData.todayAlarms}
                            precision={0}
                            className="inline"
                            duration={800}
                        />
                        <span className="ml-[2px] text-[11px]">次</span>
                    </div>
                </div>
            </div>
            
            {/* 报警记录列表 */}
            <div 
                ref={alarmListRef} 
                className="flex-1 overflow-hidden relative"
                onMouseEnter={handleMouseEnter}
                onMouseLeave={handleMouseLeave}
                onWheel={handleWheel}
            >
                <div 
                    ref={alarmScrollContainerRef}
                    className="flex flex-col gap-[4px] text-[11px] will-change-transform"
                    style={{ minHeight: '200%' }}
                >
                    {/* 报警记录项目 */}
                    <div className="flex items-center justify-between bg-[#111C23]/40 p-[6px] rounded-[3px] border border-[rgba(90,170,180,0.1)]">
                        <div className="flex items-center gap-[6px]">
                            <div className="w-[5px] h-[5px] bg-[#23C76D] rounded-full"></div>
                            <span className="text-[#CCCCCC] text-[12px]">设备状态正常</span>
                        </div>
                        <span className="text-[#909999] text-[12px]">15:30</span>
                    </div>
                    <div className="flex items-center justify-between bg-[#111C23]/40 p-[6px] rounded-[3px] border border-[rgba(90,170,180,0.1)]">
                        <div className="flex items-center gap-[6px]">
                            <div className="w-[5px] h-[5px] bg-[#E39D25] rounded-full"></div>
                            <span className="text-[#CCCCCC] text-[12px]">流量数据异常</span>
                        </div>
                        <span className="text-[#909999] text-[12px]">12:45</span>
                    </div>
                    <div className="flex items-center justify-between bg-[#111C23]/40 p-[6px] rounded-[3px] border border-[rgba(90,170,180,0.1)]">
                        <div className="flex items-center gap-[6px]">
                            <div className="w-[5px] h-[5px] bg-[#23C76D] rounded-full"></div>
                            <span className="text-[#CCCCCC] text-[12px]">通信连接恢复</span>
                        </div>
                        <span className="text-[#909999] text-[12px]">08:20</span>
                    </div>
                    <div className="flex items-center justify-between bg-[#111C23]/40 p-[6px] rounded-[3px] border border-[rgba(90,170,180,0.1)]">
                        <div className="flex items-center gap-[6px]">
                            <div className="w-[5px] h-[5px] bg-[#B83F31] rounded-full"></div>
                            <span className="text-[#CCCCCC] text-[12px]">设备离线告警</span>
                        </div>
                        <span className="text-[#909999] text-[12px]">昨日 23:15</span>
                    </div>
                    <div className="flex items-center justify-between bg-[#111C23]/40 p-[6px] rounded-[3px] border border-[rgba(90,170,180,0.1)]">
                        <div className="flex items-center gap-[6px]">
                            <div className="w-[5px] h-[5px] bg-[#E39D25] rounded-full"></div>
                            <span className="text-[#CCCCCC] text-[12px]">流量值超出阈值</span>
                        </div>
                        <span className="text-[#909999] text-[12px]">昨日 18:02</span>
                    </div>
                    <div className="flex items-center justify-between bg-[#111C23]/40 p-[6px] rounded-[3px] border border-[rgba(90,170,180,0.1)]">
                        <div className="flex items-center gap-[6px]">
                            <div className="w-[5px] h-[5px] bg-[#23C76D] rounded-full"></div>
                            <span className="text-[#CCCCCC] text-[12px]">压力传感器正常</span>
                        </div>
                        <span className="text-[#909999] text-[12px]">昨日 16:22</span>
                    </div>
                    <div className="flex items-center justify-between bg-[#111C23]/40 p-[6px] rounded-[3px] border border-[rgba(90,170,180,0.1)]">
                        <div className="flex items-center gap-[6px]">
                            <div className="w-[5px] h-[5px] bg-[#E39D25] rounded-full"></div>
                            <span className="text-[#CCCCCC] text-[12px]">温度传感器异常</span>
                        </div>
                        <span className="text-[#909999] text-[12px]">昨日 14:12</span>
                    </div>
                    <div className="flex items-center justify-between bg-[#111C23]/40 p-[6px] rounded-[3px] border border-[rgba(90,170,180,0.1)]">
                        <div className="flex items-center gap-[6px]">
                            <div className="w-[5px] h-[5px] bg-[#23C76D] rounded-full"></div>
                            <span className="text-[#CCCCCC] text-[12px]">系统自检完成</span>
                        </div>
                        <span className="text-[#909999] text-[12px]">昨日 09:00</span>
                    </div>
                    
                    {/* 重复内容实现无缝循环 */}
                    <div className="flex items-center justify-between bg-[#111C23]/40 p-[6px] rounded-[3px] border border-[rgba(90,170,180,0.1)]">
                        <div className="flex items-center gap-[6px]">
                            <div className="w-[5px] h-[5px] bg-[#23C76D] rounded-full"></div>
                            <span className="text-[#CCCCCC] text-[12px]">设备状态正常</span>
                        </div>
                        <span className="text-[#909999] text-[12px]">15:30</span>
                    </div>
                    <div className="flex items-center justify-between bg-[#111C23]/40 p-[6px] rounded-[3px] border border-[rgba(90,170,180,0.1)]">
                        <div className="flex items-center gap-[6px]">
                            <div className="w-[5px] h-[5px] bg-[#E39D25] rounded-full"></div>
                            <span className="text-[#CCCCCC] text-[12px]">流量数据异常</span>
                        </div>
                        <span className="text-[#909999] text-[12px]">12:45</span>
                    </div>
                    <div className="flex items-center justify-between bg-[#111C23]/40 p-[6px] rounded-[3px] border border-[rgba(90,170,180,0.1)]">
                        <div className="flex items-center gap-[6px]">
                            <div className="w-[5px] h-[5px] bg-[#23C76D] rounded-full"></div>
                            <span className="text-[#CCCCCC] text-[12px]">通信连接恢复</span>
                        </div>
                        <span className="text-[#909999] text-[12px]">08:20</span>
                    </div>
                    <div className="flex items-center justify-between bg-[#111C23]/40 p-[6px] rounded-[3px] border border-[rgba(90,170,180,0.1)]">
                        <div className="flex items-center gap-[6px]">
                            <div className="w-[5px] h-[5px] bg-[#B83F31] rounded-full"></div>
                            <span className="text-[#CCCCCC] text-[12px]">设备离线告警</span>
                        </div>
                        <span className="text-[#909999] text-[12px]">昨日 23:15</span>
                    </div>
                    <div className="flex items-center justify-between bg-[#111C23]/40 p-[6px] rounded-[3px] border border-[rgba(90,170,180,0.1)]">
                        <div className="flex items-center gap-[6px]">
                            <div className="w-[5px] h-[5px] bg-[#E39D25] rounded-full"></div>
                            <span className="text-[#CCCCCC] text-[12px]">流量值超出阈值</span>
                        </div>
                        <span className="text-[#909999] text-[12px]">昨日 18:02</span>
                    </div>
                    <div className="flex items-center justify-between bg-[#111C23]/40 p-[6px] rounded-[3px] border border-[rgba(90,170,180,0.1)]">
                        <div className="flex items-center gap-[6px]">
                            <div className="w-[5px] h-[5px] bg-[#23C76D] rounded-full"></div>
                            <span className="text-[#CCCCCC] text-[12px]">压力传感器正常</span>
                        </div>
                        <span className="text-[#909999] text-[12px]">昨日 16:22</span>
                    </div>
                    <div className="flex items-center justify-between bg-[#111C23]/40 p-[6px] rounded-[3px] border border-[rgba(90,170,180,0.1)]">
                        <div className="flex items-center gap-[6px]">
                            <div className="w-[5px] h-[5px] bg-[#E39D25] rounded-full"></div>
                            <span className="text-[#CCCCCC] text-[12px]">温度传感器异常</span>
                        </div>
                        <span className="text-[#909999] text-[12px]">昨日 14:12</span>
                    </div>
                    <div className="flex items-center justify-between bg-[#111C23]/40 p-[6px] rounded-[3px] border border-[rgba(90,170,180,0.1)]">
                        <div className="flex items-center gap-[6px]">
                            <div className="w-[5px] h-[5px] bg-[#23C76D] rounded-full"></div>
                            <span className="text-[#CCCCCC] text-[12px]">系统自检完成</span>
                        </div>
                        <span className="text-[#909999] text-[12px]">昨日 09:00</span>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default AlarmPage; 