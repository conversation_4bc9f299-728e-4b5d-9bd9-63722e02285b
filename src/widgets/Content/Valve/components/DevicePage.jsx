import React, { useRef, useEffect, useCallback, useState } from 'react';
import * as THREE from 'three';
import { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader.js';
import { DRACOLoader } from 'three/examples/jsm/loaders/DRACOLoader.js';
import { gsap } from 'gsap';
import NumberFlow from '@number-flow/react';

// 🎯 配置常量 - 提取魔法数字，提高可维护性
const DEVICE_CONFIG = {
    // 3D模型配置
    model: {
        scale: 3.5,
        rotationSpeed: 0.004,
        floatAmplitude: 0.05,
        floatSpeed: 0.001
    },
    // 相机配置
    camera: {
        fov: 45,
        near: 0.1,
        far: 1000,
        position: { x: 0, y: 2, z: 6 }
    },
    // 光照配置 - 增强亮度
    lighting: {
        ambient: { color: 0xffffff, intensity: 2.8 }, // 增强环境光从1.0到1.8
        directional: { color: 0xffffff, intensity: 3.4, position: { x: 8, y: 12, z: 6 } }, // 增强主方向光从1.8到2.4
        fill: { color: 0xb3e5fc, intensity: 1.2, position: { x: -8, y: 6, z: 4 } }, // 增强补充光从0.8到1.2
        accent: { color: 0x23C76D, intensity: 1.5, distance: 15, position: { x: 4, y: 6, z: 4 } } // 增强主题点光源从1.0到1.5
    },
    // 数据更新配置
    dataUpdate: {
        interval: 3000, // 3秒
        flowVariation: 5, // ±2.5的变化
        totalFlowIncrement: 0.5,
        workingHoursIncrement: 0.01
    },
    // 定时器配置
    timers: {
        init3D: 300,
        startAnimation: 100
    },
    // 🎬 入场动画配置
    entranceAnimation: {
        // 缩放动画
        scale: {
            duration: 0.8,
            ease: "back.out(1.2)",
            delay: 0
        },
        // 位置动画
        position: {
            duration: 0.8,
            ease: "power2.out",
            delay: 0,
            initialOffset: { x: 0, y: -2, z: 0 }
        },
        // 旋转动画
        rotation: {
            duration: 0.6,
            ease: "power2.out",
            delay: 0.2,
            initialRotation: { x: 0, y: Math.PI * 0.5, z: 0 }
        },
        // 透明度动画
        opacity: {
            duration: 0.5,
            ease: "power2.out",
            delay: 0.3
        }
    }
};

/**
 * 设备页面组件 - 3D模型展示
 * 显示流量计设备的3D模型，支持鼠标交互
 */
const DevicePage = ({ 
    threeDContainerRef,
    threeSceneRef,
    threeRendererRef,
    threeCameraRef,
    flowmeterModelRef,
    threeAnimationIdRef,
    mouseInteraction,
    setMouseInteraction,
    handleMouseDown,
    handleMouseMove,
    handleMouseUp,
    handleMouseLeave,
    handleMouseWheel,
    rotationStateRef,
    resourceRefs
}) => {
    
    // 自动旋转状态管理
    const [autoRotate, setAutoRotate] = useState(true);
    
    // 同步状态
    useEffect(() => {
        rotationStateRef.current.autoRotate = autoRotate;
        rotationStateRef.current.userAutoRotateEnabled = autoRotate; // 记录用户的选择
    }, [autoRotate]);
    
    // 实时数据状态
    const [realTimeData, setRealTimeData] = useState({
        currentFlow: 128.5,
        totalFlow: 45231,
        workingHours: 2468
    });
    
    // 生成模拟数据变化
    const generateRealtimeData = useCallback(() => {
        setRealTimeData(prev => ({
            currentFlow: Math.max(0, prev.currentFlow + (Math.random() - 0.5) * DEVICE_CONFIG.dataUpdate.flowVariation),
            totalFlow: prev.totalFlow + Math.random() * DEVICE_CONFIG.dataUpdate.totalFlowIncrement,
            workingHours: prev.workingHours + Math.random() * DEVICE_CONFIG.dataUpdate.workingHoursIncrement
        }));
    }, []);

    // 🚀 性能优化：动态管理GPU加速 - 只在拖拽时启用will-change
    useEffect(() => {
        if (threeDContainerRef.current) {
            const container = threeDContainerRef.current;
            
            if (mouseInteraction.isDragging || mouseInteraction.isMouseDown) {
                // 拖拽时启用GPU加速
                container.style.willChange = 'transform, opacity';
                container.style.transform = 'translateZ(0)'; // 强制GPU加速
            } else {
                // 不拖拽时关闭GPU加速，节省资源
                container.style.willChange = 'auto';
                container.style.transform = 'none';
            }
        }
    }, [mouseInteraction.isDragging, mouseInteraction.isMouseDown]);

    // 🔄 重置模型状态函数
    const resetModelState = useCallback(() => {
        if (rotationStateRef.current) {
            // 使用GSAP平滑重置所有状态，但保持当前的autoRotate设置
            gsap.to(rotationStateRef.current, {
                rotationX: 0,
                rotationY: 0,
                zoom: 1.0,
                panX: 0,
                panY: 0,
                duration: 0.8,
                ease: "power2.out"
                // 移除onComplete中的autoRotate强制设置
            });
        }
    }, []);

    // 创建3D流量计模型
    const create3DFlowmeterModel = useCallback(() => {
        if (!threeDContainerRef.current) return;

        // 清理之前的Three.js资源
        if (threeRendererRef.current) {
            threeRendererRef.current.dispose();
            threeRendererRef.current.forceContextLoss();
            
            if (threeDContainerRef.current && threeRendererRef.current.domElement) {
                try {
                    threeDContainerRef.current.removeChild(threeRendererRef.current.domElement);
                } catch (e) {
                    console.warn('移除3D渲染器DOM元素时出错:', e);
                }
            }
        }

        // 创建场景、相机和渲染器
        const scene = new THREE.Scene();
        scene.background = null; // 透明背景
        
        const camera = new THREE.PerspectiveCamera(
            DEVICE_CONFIG.camera.fov,
            threeDContainerRef.current.clientWidth / threeDContainerRef.current.clientHeight,
            DEVICE_CONFIG.camera.near,
            DEVICE_CONFIG.camera.far
        );
        
        const renderer = new THREE.WebGLRenderer({ 
            antialias: true,
            alpha: true
        });
        renderer.setSize(
            threeDContainerRef.current.clientWidth, 
            threeDContainerRef.current.clientHeight
        );
        renderer.setClearColor(0x000000, 0);
        
        // 启用阴影渲染，让GLB模型更有立体感// 启用阴影渲染，增强模型立体感
        renderer.shadowMap.enabled = true;
        renderer.shadowMap.type = THREE.PCFSoftShadowMap;
        
        // 启用物理正确光照和色调映射
        renderer.physicallyCorrectLights = true;
        renderer.toneMapping = THREE.ACESFilmicToneMapping;
        renderer.toneMappingExposure = 1.6; // 增强曝光度从1.0到1.6，让模型更亮
        
        threeDContainerRef.current.appendChild(renderer.domElement);
        
        // 存储引用
        threeSceneRef.current = scene;
        threeCameraRef.current = camera;
        threeRendererRef.current = renderer;

        // 加载GLB模型
        const flowmeterGroup = new THREE.Group();
        const loader = new GLTFLoader();
        
        // 🔧 配置DRACOLoader以支持压缩的GLB模型
        const dracoLoader = new DRACOLoader();
        // 设置Draco解码器路径 - 使用CDN或本地路径
        dracoLoader.setDecoderPath('https://www.gstatic.com/draco/versioned/decoders/1.5.6/');
        // 可选：设置解码器配置
        dracoLoader.setDecoderConfig({ type: 'js' });
        // 将DRACOLoader设置给GLTFLoader
        loader.setDRACOLoader(dracoLoader);
        
        // 异步加载GLB模型 - 修正路径为public目录下的资源
        loader.load(
            // '/assets/3d/flowmeter.glb',
            '/assets/3d/valve.glb',
            (gltf) => {
                // 加载成功的回调函数
                
                const model = gltf.scene;
                
                // 🎬 设置模型初始状态 - 为入场动画做准备
                model.scale.set(0, 0, 0); // 初始缩放为0，准备放大动画
                model.position.set(
                    DEVICE_CONFIG.entranceAnimation.position.initialOffset.x,
                    DEVICE_CONFIG.entranceAnimation.position.initialOffset.y,
                    DEVICE_CONFIG.entranceAnimation.position.initialOffset.z
                ); // 初始位置偏移
                model.rotation.set(
                    DEVICE_CONFIG.entranceAnimation.rotation.initialRotation.x,
                    DEVICE_CONFIG.entranceAnimation.rotation.initialRotation.y,
                    DEVICE_CONFIG.entranceAnimation.rotation.initialRotation.z
                ); // 初始旋转角度
                
                // 设置模型透明度为0（需要遍历所有材质）
                model.traverse((child) => {
                    if (child.isMesh) {
                        // 启用阴影增强立体感
            child.castShadow = true;
            child.receiveShadow = true;
                        
                        // 优化材质，营造电镀银色发亮效果
                         if (child.material) {
                             // 创建新的标准材质以获得更好的电镀效果
                             const originalMaterial = child.material;
                             
                             // 银白色电镀材质配置
                              child.material = originalMaterial; // 恢复到原始材质
                              child.material.transparent = true; // 确保透明度设置以便动画
                              child.material.opacity = 0;      // 初始透明度为0
                              child.material.needsUpdate = true;
                             
                             // 如果原材质有贴图，保留它们
                             if (originalMaterial.map) {
                                 child.material.map = originalMaterial.map;
                             }
                             if (originalMaterial.normalMap) {
                                 child.material.normalMap = originalMaterial.normalMap;
                             }
                             
                             child.material.needsUpdate = true;
                        }
                    }
                });
                
                // 将模型添加到组中
                flowmeterGroup.add(model);
                
                // 🎭 执行入场动画 - 使用GSAP创建流畅的入场效果
                const timeline = gsap.timeline({
                    // onComplete: () => {
                    //     console.log('3D模型入场动画完成');
                    // }
                });
                
                // 第一阶段：缩放动画
                timeline.to(model.scale, {
                    x: DEVICE_CONFIG.model.scale,
                    y: DEVICE_CONFIG.model.scale, 
                    z: DEVICE_CONFIG.model.scale,
                    duration: DEVICE_CONFIG.entranceAnimation.scale.duration,
                    ease: DEVICE_CONFIG.entranceAnimation.scale.ease
                }, DEVICE_CONFIG.entranceAnimation.scale.delay);
                
                // 第二阶段：位置动画（与缩放同时进行）
                timeline.to(model.position, {
                    x: 0,
                    y: 0,
                    z: 0,
                    duration: DEVICE_CONFIG.entranceAnimation.position.duration,
                    ease: DEVICE_CONFIG.entranceAnimation.position.ease
                }, DEVICE_CONFIG.entranceAnimation.position.delay);
                
                // 第三阶段：旋转动画
                timeline.to(model.rotation, {
                    x: 0,
                    y: 0,
                    z: 0,
                    duration: DEVICE_CONFIG.entranceAnimation.rotation.duration,
                    ease: DEVICE_CONFIG.entranceAnimation.rotation.ease
                }, DEVICE_CONFIG.entranceAnimation.rotation.delay);
                
                // 第四阶段：透明度动画
                timeline.call(() => {
                    // 遍历所有材质，逐渐显示
                    model.traverse((child) => {
                        if (child.isMesh && child.material) {
                            gsap.to(child.material, {
                                opacity: 1,
                                duration: DEVICE_CONFIG.entranceAnimation.opacity.duration,
                                ease: DEVICE_CONFIG.entranceAnimation.opacity.ease
                            });
                        }
                    });
                }, [], DEVICE_CONFIG.entranceAnimation.opacity.delay);
                
            },
            (progress) => {
                // 🔍 加载进度回调 - 打印模型文件大小和加载进度
                const percentComplete = (progress.loaded / progress.total) * 100;
                const fileSizeMB = (progress.total / (1024 * 1024)).toFixed(2);
                const loadedSizeMB = (progress.loaded / (1024 * 1024)).toFixed(2);
                
                // 当加载完成时打印最终信息
                // if (percentComplete >= 100) {
                //     console.log(`✅ GLB模型加载完成！总文件大小: ${fileSizeMB}MB`);
                // }
            },
            (error) => {
                // 加载错误回调
                // console.warn('GLB模型加载失败，使用替代模型:', error);
                
                // 加载失败时创建一个简单的替代模型
                const fallbackGeometry = new THREE.BoxGeometry(1, 0.5, 0.3);
                const fallbackMaterial = new THREE.MeshPhongMaterial({ 
                    color: 0x23C76D,
                    transparent: true,
                    opacity: 0 // 初始透明度为0
                });
                const fallbackMesh = new THREE.Mesh(fallbackGeometry, fallbackMaterial);
                
                // 🎬 设置替代模型初始状态
                fallbackMesh.scale.set(0, 0, 0);
                fallbackMesh.position.set(
                    DEVICE_CONFIG.entranceAnimation.position.initialOffset.x,
                    DEVICE_CONFIG.entranceAnimation.position.initialOffset.y * 0.5, // 替代模型位移稍小
                    DEVICE_CONFIG.entranceAnimation.position.initialOffset.z
                );
                fallbackMesh.rotation.set(
                    DEVICE_CONFIG.entranceAnimation.rotation.initialRotation.x,
                    DEVICE_CONFIG.entranceAnimation.rotation.initialRotation.y * 0.5, // 替代模型旋转角度稍小
                    DEVICE_CONFIG.entranceAnimation.rotation.initialRotation.z
                );
                
                flowmeterGroup.add(fallbackMesh);
                
                // 🎭 执行替代模型入场动画
                const fallbackTimeline = gsap.timeline({
                    // onComplete: () => {
                    //     console.log('替代模型入场动画完成');
                    // }
                });
                
                // 缩放动画
                fallbackTimeline.to(fallbackMesh.scale, {
                    x: DEVICE_CONFIG.model.scale,
                    y: DEVICE_CONFIG.model.scale,
                    z: DEVICE_CONFIG.model.scale,
                    duration: DEVICE_CONFIG.entranceAnimation.scale.duration,
                    ease: DEVICE_CONFIG.entranceAnimation.scale.ease
                }, DEVICE_CONFIG.entranceAnimation.scale.delay);
                
                // 位置动画
                fallbackTimeline.to(fallbackMesh.position, {
                    x: 0,
                    y: 0,
                    z: 0,
                    duration: DEVICE_CONFIG.entranceAnimation.position.duration,
                    ease: DEVICE_CONFIG.entranceAnimation.position.ease
                }, DEVICE_CONFIG.entranceAnimation.position.delay);
                
                // 旋转动画
                fallbackTimeline.to(fallbackMesh.rotation, {
                    x: 0,
                    y: 0,
                    z: 0,
                    duration: DEVICE_CONFIG.entranceAnimation.rotation.duration,
                    ease: DEVICE_CONFIG.entranceAnimation.rotation.ease
                }, DEVICE_CONFIG.entranceAnimation.rotation.delay);
                
                // 透明度动画
                fallbackTimeline.to(fallbackMaterial, {
                    opacity: 0.8,
                    duration: DEVICE_CONFIG.entranceAnimation.opacity.duration,
                    ease: DEVICE_CONFIG.entranceAnimation.opacity.ease
                }, DEVICE_CONFIG.entranceAnimation.opacity.delay);
                
            }
        );

        scene.add(flowmeterGroup);
        flowmeterModelRef.current = flowmeterGroup;

        // 设置相机位置 - 调整为更正对的视角
        camera.position.set(DEVICE_CONFIG.camera.position.x, DEVICE_CONFIG.camera.position.y, DEVICE_CONFIG.camera.position.z);
        camera.lookAt(0, 0, 0); // 相机朝向模型中心

        // 🔥 优化光照系统 - 精简光源配置，提升性能
        // 环境光 - 提供强烈基础照明，营造银白色电镀效果
        const ambientLight = new THREE.AmbientLight(DEVICE_CONFIG.lighting.ambient.color, DEVICE_CONFIG.lighting.ambient.intensity); // 恢复默认环境光
        scene.add(ambientLight);

        // 主方向光
        const directionalLight = new THREE.DirectionalLight(DEVICE_CONFIG.lighting.directional.color, DEVICE_CONFIG.lighting.directional.intensity);
        directionalLight.position.set(DEVICE_CONFIG.lighting.directional.position.x, DEVICE_CONFIG.lighting.directional.position.y, DEVICE_CONFIG.lighting.directional.position.z);
        directionalLight.castShadow = true;
        directionalLight.shadow.mapSize.width = 2048;
        directionalLight.shadow.mapSize.height = 2048;
        directionalLight.shadow.camera.near = 0.5;
        directionalLight.shadow.camera.far = 30;
        directionalLight.shadow.camera.left = -5;
        directionalLight.shadow.camera.right = 5;
        directionalLight.shadow.camera.top = 5;
        directionalLight.shadow.camera.bottom = -5;
        scene.add(directionalLight);

        // 补充光源
        const fillLight = new THREE.DirectionalLight(DEVICE_CONFIG.lighting.fill.color, DEVICE_CONFIG.lighting.fill.intensity);
        fillLight.position.set(DEVICE_CONFIG.lighting.fill.position.x, DEVICE_CONFIG.lighting.fill.position.y, DEVICE_CONFIG.lighting.fill.position.z);
        scene.add(fillLight);
        


        // 主题点光源
        const accentLight = new THREE.PointLight(DEVICE_CONFIG.lighting.accent.color, DEVICE_CONFIG.lighting.accent.intensity, DEVICE_CONFIG.lighting.accent.distance);
        accentLight.position.set(DEVICE_CONFIG.lighting.accent.position.x, DEVICE_CONFIG.lighting.accent.position.y, DEVICE_CONFIG.lighting.accent.position.z);
        accentLight.decay = 1.5; // 减少衰减，让光照更强
        scene.add(accentLight);


    }, []);

    // 3D模型动画循环 - 性能优化版本
    const animate3DModel = useCallback(() => {
        if (!threeRendererRef.current || !threeSceneRef.current || !threeCameraRef.current) {
            return;
        }

        // 🚀 性能优化：缓存常用引用，避免每帧重复访问
        const renderer = threeRendererRef.current;
        const scene = threeSceneRef.current;
        const camera = threeCameraRef.current;
        const model = flowmeterModelRef.current;
        const rotationState = rotationStateRef.current;
        const container = threeDContainerRef.current;

        // 🚀 性能优化：使用更高效的尺寸检查，降低检查频率
        // 每10帧检查一次尺寸变化，而不是每帧都检查
        let shouldCheckResize = false;
        if (!animate3DModel.frameCount) animate3DModel.frameCount = 0;
        animate3DModel.frameCount++;
        
        // 只在特定条件下检查尺寸：拖拽中或每10帧检查一次
        if (mouseInteraction.isDragging || animate3DModel.frameCount % 10 === 0) {
            shouldCheckResize = true;
        }

        if (shouldCheckResize && container) {
            const rect = container.getBoundingClientRect();
            const currentWidth = renderer.domElement.clientWidth;
            const currentHeight = renderer.domElement.clientHeight;
            
            // 🚀 性能优化：只在有实质性尺寸变化时才调整（阈值提高到3px）
            if (Math.abs(rect.width - currentWidth) > 3 || Math.abs(rect.height - currentHeight) > 3) {
                renderer.setSize(rect.width, rect.height);
                
                if (camera) {
                    camera.aspect = rect.width / rect.height;
                    camera.updateProjectionMatrix();
                }
            }
        }

        // 🚀 性能优化：批量处理模型变换，减少Three.js对象访问
        if (model && rotationState) {
            // 🎯 自动旋转优化：只在需要时更新Y轴旋转
            if (rotationState.autoRotate && !mouseInteraction.isMouseDown) {
                rotationState.rotationY += DEVICE_CONFIG.model.rotationSpeed; // 使用配置中的旋转速度
            }
            
            // 🚀 性能优化：批量设置旋转，避免多次调用Three.js API
            model.rotation.set(rotationState.rotationX, rotationState.rotationY, 0);
            
            // 🚀 性能优化：批量设置位置
            model.position.set(rotationState.panX, rotationState.panY, 0);
        }
        
        // 🚀 性能优化：相机变换优化，缓存计算结果
        if (camera && rotationState) {
            const basePosition = DEVICE_CONFIG.camera.position;
            const zoomFactor = rotationState.zoom;
            
            // 🚀 性能优化：缓存方向向量计算（在初始化时计算一次）
            if (!animate3DModel.cachedDirectionVector) {
                animate3DModel.cachedDirectionVector = {
                    x: basePosition.x,
                    y: basePosition.y,
                    z: basePosition.z
                };
            }
            
            const dirVector = animate3DModel.cachedDirectionVector;
            
            // 🚀 性能优化：批量设置相机位置，减少Three.js API调用
            camera.position.set(
                dirVector.x / zoomFactor,
                dirVector.y / zoomFactor,
                dirVector.z / zoomFactor
            );
            
            // 🚀 性能优化：只在相机位置真正改变时才调用lookAt
            if (animate3DModel.lastZoom !== zoomFactor) {
                camera.lookAt(0, 0, 0);
                animate3DModel.lastZoom = zoomFactor;
            }
        }

        // 🚀 性能优化：渲染调用优化
        renderer.render(scene, camera);

        // 继续动画循环
        threeAnimationIdRef.current = requestAnimationFrame(animate3DModel);
    }, []);

    // 初始化3D模型
    useEffect(() => {
        const init3DTimer = setTimeout(() => {
            create3DFlowmeterModel();
            
            const startAnimationTimer = setTimeout(() => {
                animate3DModel();
            }, 100);
            
            resourceRefs.current.timeouts.push(startAnimationTimer);
        }, 300);
        
        resourceRefs.current.timeouts.push(init3DTimer);
        
        // 启动实时数据模拟定时器
        const dataTimer = setInterval(generateRealtimeData, 3000); // 每3秒更新一次数据
        resourceRefs.current.intervals.push(dataTimer);
        
        return () => {
            clearTimeout(init3DTimer);
            clearInterval(dataTimer);
        };
    }, [create3DFlowmeterModel, animate3DModel, generateRealtimeData]);

    return (
        <div className="device-page w-full h-full flex flex-row">
            {/* 3D模型容器 - 占据左侧2/3空间 */}
            <div className="w-2/3 h-full flex flex-col">
                <div 
                    ref={threeDContainerRef}
                    className="threejs-container w-full h-full bg-transparent relative overflow-hidden cursor-grab"
                    style={{
                        minHeight: '120px',
                        background: 'radial-gradient(ellipse at center, rgba(35, 199, 109, 0.03) 0%, rgba(35, 199, 109, 0.01) 30%, transparent 60%)', // 缩小绿色阴影扩散范围
                        cursor: mouseInteraction.isDragging ? 'grabbing' : (mouseInteraction.isMouseDown ? 'grabbing' : 'grab')
                    }}
                    onMouseDown={handleMouseDown}
                onMouseMove={handleMouseMove}
                onMouseUp={handleMouseUp}
                onMouseLeave={handleMouseLeave}
                onWheel={handleMouseWheel}
                onContextMenu={(e) => e.preventDefault()} // 禁用右键菜单
                >
                    {/* 控制按钮组 */}
                    <div className="absolute top-[12px] right-[12px] pointer-events-auto flex flex-col space-y-[8px]">
                        {/* 自动旋转开关按钮 */}
                        <button
                            onClick={() => setAutoRotate(!autoRotate)}
                            className={`px-[12px] py-[6px] ${autoRotate ? 'bg-gradient-to-br from-[rgba(35,199,109,0.25)] to-[rgba(35,199,109,0.15)]' : 'bg-gradient-to-br from-[rgba(100,100,100,0.15)] to-[rgba(80,80,80,0.05)]'} hover:from-[rgba(35,199,109,0.35)] hover:to-[rgba(35,199,109,0.25)] border ${autoRotate ? 'border-[rgba(35,199,109,0.6)]' : 'border-[rgba(100,100,100,0.4)]'} hover:border-[rgba(35,199,109,0.8)] rounded-[6px] flex items-center justify-center transition-all duration-300 shadow-lg hover:shadow-[0_0_20px_rgba(35,199,109,0.3)] backdrop-blur-[4px] relative`}
                            title={autoRotate ? "关闭自动旋转" : "开启自动旋转"}
                        >
                            {/* 激活状态标识 */}
                            {autoRotate && (
                                <div className="absolute left-[-6px] top-1/2 transform -translate-y-1/2 w-[3px] h-[16px] bg-[rgba(35,199,109,0.8)] rounded-full"></div>
                            )}
                            <span className={`text-[12px] font-medium ${autoRotate ? 'text-[rgba(35,199,109,0.9)]' : 'text-[rgba(120,120,120,0.8)]'}`}>
                                自动旋转
                            </span>
                        </button>
                        
                        {/* 重置按钮 */}
                        <button
                            onClick={resetModelState}
                            className="px-[12px] py-[6px] bg-gradient-to-br from-[rgba(35,199,109,0.15)] to-[rgba(35,199,109,0.05)] hover:from-[rgba(35,199,109,0.25)] hover:to-[rgba(35,199,109,0.15)] border border-[rgba(35,199,109,0.4)] hover:border-[rgba(35,199,109,0.6)] rounded-[6px] flex items-center justify-center transition-all duration-300 shadow-lg hover:shadow-[0_0_20px_rgba(35,199,109,0.3)] backdrop-blur-[4px]"
                            title="重置模型状态"
                        >
                            <span className="text-[12px] font-medium text-[rgba(35,199,109,0.9)]">
                                重置
                            </span>
                        </button>
                    </div>

                    {/* 3D交互提示 */}
                    <div className="absolute bottom-[10px] left-[10px] text-[12px] text-white/60 pointer-events-none">
                        <div className="flex items-center space-x-[8px]">
                            <div className="w-[16px] h-[16px] border border-white/40 rounded-[2px] flex items-center justify-center">
                                <span className="text-[10px]">🖱️</span>
                            </div>
                            <span>左键旋转 • 右键移动 • 滚轮缩放 • 自动旋转</span>
                        </div>
                    </div>
                </div>
            </div>

            {/* 设备信息面板 - 占据右侧1/3空间 */}
            <div className="w-1/3 h-full bg-gradient-to-b from-[rgba(20,25,35,0.5)] to-[rgba(15,20,30,0.6)] backdrop-blur-[8px] border-l border-[rgba(35,199,109,0.3)] flex flex-col">
                {/* 信息面板标题 */}
                <div className="px-[10px] border-b border-[rgba(35,199,109,0.2)]">
                    <h3 className="text-[14px] font-bold text-[#23C76D] mb-[1px]">设备信息</h3>
                    <p className="text-[10px] text-white/70">Device Information</p>
                </div>

                {/* 设备基本信息 */}
                <div className="flex-1 p-[16px] overflow-y-auto">
                    <div className="space-y-[12px]">
                        {/* 设备型号 */}
                        <div className="bg-[rgba(35,199,109,0.05)] rounded-[8px] p-[12px] border border-[rgba(35,199,109,0.1)]">
                            <div className="text-[12px] text-white/60 mb-[4px]">设备型号</div>
                            <div className="text-[14px] font-medium text-white">JHL-FM-3000</div>
                        </div>

                        {/* 设备状态 */}
                        <div className="bg-[rgba(35,199,109,0.05)] rounded-[8px] p-[12px] border border-[rgba(35,199,109,0.1)]">
                            <div className="flex items-center justify-between">
                                <div className="text-[12px] text-white/60">运行状态</div>
                                <div className="flex items-center space-x-[6px]">
                                    <div className="w-[8px] h-[8px] bg-[#23C76D] rounded-full animate-pulse"></div>
                                    <span className="text-[14px] font-medium text-[#23C76D]">正常运行</span>
                                </div>
                            </div>
                        </div>

                        {/* 现场照片 */}
                        <div className="bg-[rgba(35,199,109,0.05)] rounded-[8px] p-[12px] border border-[rgba(35,199,109,0.1)]">
                            <div className="text-[12px] text-white/60 mb-[8px]">现场照片</div>
                            <div className="grid grid-cols-3 gap-[4px]">
                                {Array.from({ length: 9 }, (_, index) => (
                                    <div 
                                        key={index}
                                        className="aspect-square bg-gradient-to-br from-[rgba(35,199,109,0.1)] to-[rgba(35,199,109,0.05)] rounded-[3px] border border-[rgba(35,199,109,0.1)] flex items-center justify-center cursor-pointer hover:bg-[rgba(35,199,109,0.2)] transition-colors duration-200"
                                        title={`现场照片 ${index + 1}`}
                                    >
                                        <div className="text-[8px] text-white/60">
                                            {index < 6 ? '📷' : index < 8 ? '🔧' : '📊'}
                                        </div>
                                    </div>
                                ))}
                            </div>
                        </div>

                        {/* 技术参数 */}
                        <div className="space-y-[8px]">
                            <h4 className="text-[14px] font-medium text-white border-b border-[rgba(35,199,109,0.2)] pb-[4px]">技术参数</h4>
                            
                            <div className="space-y-[6px] text-[12px]">
                                <div className="flex justify-between">
                                    <span className="text-white/70">测量范围:</span>
                                    <span className="text-white">0.5-1000 m³/h</span>
                                </div>
                                <div className="flex justify-between">
                                    <span className="text-white/70">精度等级:</span>
                                    <span className="text-white">±0.5%</span>
                                </div>
                                <div className="flex justify-between">
                                    <span className="text-white/70">工作压力:</span>
                                    <span className="text-white">≤1.6 MPa</span>
                                </div>
                                <div className="flex justify-between">
                                    <span className="text-white/70">工作温度:</span>
                                    <span className="text-white">-20℃~+120℃</span>
                                </div>
                                <div className="flex justify-between">
                                    <span className="text-white/70">口径规格:</span>
                                    <span className="text-white">DN50</span>
                                </div>
                            </div>
                        </div>

                        {/* 安装信息 */}
                        <div className="space-y-[8px]">
                            <h4 className="text-[14px] font-medium text-white border-b border-[rgba(35,199,109,0.2)] pb-[4px]">安装信息</h4>
                            
                            <div className="space-y-[6px] text-[12px]">
                                <div className="flex justify-between items-center">
                                    <span className="text-white/70">安装位置:</span>
                                    <div className="flex items-center space-x-[4px]">
                                        <span className="text-white">主管道A段</span>
                                        <button className="w-[16px] h-[16px] bg-[rgba(35,199,109,0.2)] hover:bg-[rgba(35,199,109,0.4)] rounded-[2px] flex items-center justify-center transition-colors duration-200 cursor-pointer" title="定位到安装位置">
                                            <span className="text-[10px] text-[#23C76D]">📍</span>
                                        </button>
                                    </div>
                                </div>
                                <div className="flex justify-between">
                                    <span className="text-white/70">安装日期:</span>
                                    <span className="text-white">2024-01-15</span>
                                </div>
                                <div className="flex justify-between">
                                    <span className="text-white/70">维护周期:</span>
                                    <span className="text-white">6个月</span>
                                </div>
                                <div className="flex justify-between">
                                    <span className="text-white/70">下次维护:</span>
                                    <span className="text-orange-400">2024-07-15</span>
                                </div>
                            </div>
                        </div>

                        {/* 通信信息 */}
                        <div className="space-y-[8px]">
                            <h4 className="text-[14px] font-medium text-white border-b border-[rgba(35,199,109,0.2)] pb-[4px]">通信信息</h4>
                            
                            <div className="space-y-[6px] text-[12px]">
                                <div className="flex justify-between">
                                    <span className="text-white/70">通信协议:</span>
                                    <span className="text-white">Modbus RTU</span>
                                </div>
                                <div className="flex justify-between">
                                    <span className="text-white/70">设备地址:</span>
                                    <span className="text-white">192.168.1.100</span>
                                </div>
                                <div className="flex justify-between">
                                    <span className="text-white/70">端口号:</span>
                                    <span className="text-white">502</span>
                                </div>
                                <div className="flex justify-between">
                                    <span className="text-white/70">信号强度:</span>
                                    <div className="flex items-center space-x-[4px]">
                                        <div className="w-[8px] h-[8px] bg-[#23C76D] rounded-full"></div>
                                        <span className="text-[#23C76D]">强</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        {/* 实时数据摘要 */}
                        <div className="space-y-[8px]">
                            <h4 className="text-[14px] font-medium text-white border-b border-[rgba(35,199,109,0.2)] pb-[4px]">实时数据</h4>
                            
                            <div className="grid grid-cols-1 gap-[8px]">
                                <div className="bg-[rgba(35,199,109,0.1)] rounded-[6px] p-[8px] text-center">
                                    <div className="text-[10px] text-white/60">当前流量</div>
                                    <div className="text-[16px] font-bold text-[#23C76D] font-mono">
                                        <NumberFlow 
                                            value={realTimeData.currentFlow} 
                                            precision={1} 
                                            duration={800}
                                            className="inline"
                                        />
                                    </div>
                                    <div className="text-[10px] text-white/60">m³/h</div>
                                </div>
                                
                                <div className="grid grid-cols-2 gap-[8px]">
                                    <div className="bg-[rgba(35,199,109,0.1)] rounded-[6px] p-[8px] text-center">
                                        <div className="text-[10px] text-white/60">累计流量</div>
                                        <div className="text-[12px] font-bold text-white font-mono">
                                            <NumberFlow 
                                                value={realTimeData.totalFlow} 
                                                precision={0} 
                                                duration={1200}
                                                className="inline"
                                            />
                                        </div>
                                        <div className="text-[10px] text-white/60">m³</div>
                                    </div>
                                    <div className="bg-[rgba(35,199,109,0.1)] rounded-[6px] p-[8px] text-center">
                                        <div className="text-[10px] text-white/60">工作时长</div>
                                        <div className="text-[12px] font-bold text-white font-mono">
                                            <NumberFlow 
                                                value={realTimeData.workingHours} 
                                                precision={0} 
                                                duration={1000}
                                                className="inline"
                                            />
                                        </div>
                                        <div className="text-[10px] text-white/60">小时</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                {/* 底部操作按钮 */}
                <div className="p-[20px] border-t border-[rgba(35,199,109,0.2)]">
                    <div className="space-y-[8px]">
                        <button className="w-full bg-[rgba(35,199,109,0.1)] hover:bg-[rgba(35,199,109,0.2)] border border-[rgba(35,199,109,0.3)] text-[#23C76D] text-[12px] py-[8px] rounded-[4px] transition-colors duration-200">
                            查看详细参数
                        </button>
                        <button className="w-full bg-[rgba(35,199,109,0.1)] hover:bg-[rgba(35,199,109,0.2)] border border-[rgba(35,199,109,0.3)] text-[#23C76D] text-[12px] py-[8px] rounded-[4px] transition-colors duration-200">
                            设备维护记录
                        </button>
                    </div>
                </div>
            </div>
        </div>
    );
    };

export default DevicePage;