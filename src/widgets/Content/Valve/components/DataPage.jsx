import React, { useRef, useEffect } from 'react';
import NumberFlow from '@number-flow/react';

/**
 * 数据页面组件
 * 显示流量相关的各种维度数据
 */
const DataPage = ({ 
    dimensionData, 
    dimensionFlashing,
    dimensionRefs 
}) => {
    return (
        <div className="data-page w-full h-full flex flex-col gap-[10px]">
            <div className="grid grid-cols-2 gap-[8px] text-[12px] pt-[5px]">
                {/* 今日用量 */}
                <div 
                    ref={el => dimensionRefs.current.todayUsage = el}
                    className="relative bg-gradient-to-br from-[#1a1a1d]/60 to-[#2A2A2E]/60 p-[8px] border border-[#23C76D]/20 transition-all duration-300 overflow-hidden"
                >
                    <div className="text-[#909999] mb-[4px] font-['DingTalkJinBuTi']">今日用量</div>
                    <div className="text-[#23C76D] font-medium font-mono tracking-wider">
                        <NumberFlow
                            value={parseFloat(dimensionData.todayUsage.toFixed(1))}
                            precision={1}
                            className="inline"
                            duration={800}
                        />
                        <span className="ml-[2px]">m³</span>
                    </div>
                </div>
                
                {/* 本月用量 */}
                <div 
                    ref={el => dimensionRefs.current.monthUsage = el}
                    className="relative bg-gradient-to-br from-[#1a1a1d]/60 to-[#2A2A2E]/60 p-[8px] border border-[#23C76D]/20 transition-all duration-300 overflow-hidden"
                >
                    <div className="text-[#909999] mb-[4px] font-['DingTalkJinBuTi']">本月用量</div>
                    <div className="text-[#23C76D] font-medium font-mono tracking-wider">
                        <NumberFlow
                            value={parseFloat(dimensionData.monthUsage.toFixed(1))}
                            precision={1}
                            className="inline"
                            duration={800}
                        />
                        <span className="ml-[2px]">m³</span>
                    </div>
                </div>
                
                {/* 平均流量 */}
                <div 
                    ref={el => dimensionRefs.current.avgFlow = el}
                    className="relative bg-gradient-to-br from-[#1a1a1d]/60 to-[#2A2A2E]/60 p-[8px] border border-[#96E072]/20 transition-all duration-300 overflow-hidden"
                >
                    <div className="text-[#909999] mb-[4px] font-['DingTalkJinBuTi']">平均流量</div>
                    <div className="text-[#96E072] font-medium font-mono tracking-wider">
                        <NumberFlow
                            value={parseFloat(dimensionData.avgFlow.toFixed(1))}
                            precision={1}
                            className="inline"
                            duration={800}
                        />
                        <span className="ml-[2px]">m³/h</span>
                    </div>
                </div>
                
                {/* 峰值流量 */}
                <div 
                    ref={el => dimensionRefs.current.peakFlow = el}
                    className="relative bg-gradient-to-br from-[#1a1a1d]/60 to-[#2A2A2E]/60 p-[8px] border border-[#96E072]/20 transition-all duration-300 overflow-hidden"
                >
                    <div className="text-[#909999] mb-[4px] font-['DingTalkJinBuTi']">峰值流量</div>
                    <div className="text-[#96E072] font-medium font-mono tracking-wider">
                        <NumberFlow
                            value={parseFloat(dimensionData.peakFlow.toFixed(1))}
                            precision={1}
                            className="inline"
                            duration={800}
                        />
                        <span className="ml-[2px]">m³/h</span>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default DataPage; 